import os
import sys
import subprocess
import pathlib
import numpy as np
from PIL import Image
import cv2

# ---------- 0) Small helpers ----------
def pip_install(pkg: str):
    try:
        __import__(pkg.split("==")[0].split("[")[0].replace("-", "_"))
    except Exception:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg])

ROOT = pathlib.Path.cwd()
OUT_DIR = ROOT / "avatar_outputs"
OUT_DIR.mkdir(exist_ok=True)

# ---------- 1) Dependencies ----------
pip_install("opencv-python")
pip_install("Pillow")
pip_install("scikit-image")

# ---------- 2) Setup ----------
print("Setting up advanced image processing for realistic avatars...")

try:
    from skimage import restoration, filters, exposure, morphology
    print("✓ Scikit-image loaded successfully")
    SKIMAGE_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Scikit-image not available: {e}")
    SKIMAGE_AVAILABLE = False

print("Avatar processing ready!")

# ---------- 3) Advanced image enhancement functions ----------
def enhance_with_scikit_image(img):
    """Apply advanced image enhancement using scikit-image."""
    if not SKIMAGE_AVAILABLE:
        return img

    try:
        print("Applying advanced image enhancement...")

        # Convert to float for processing (0-1 range)
        img_float = img.astype(np.float64) / 255.0

        # Apply unsharp masking for sharpening
        from skimage import filters
        gaussian = filters.gaussian(img_float, sigma=1.0, channel_axis=2)
        img_sharp = img_float + 0.5 * (img_float - gaussian)
        img_sharp = np.clip(img_sharp, 0, 1)

        # Enhance contrast using adaptive histogram equalization
        from skimage import exposure
        img_enhanced = exposure.equalize_adapthist(img_sharp, clip_limit=0.02)

        # Apply edge-preserving smoothing using OpenCV bilateral filter
        img_smooth = img_enhanced.copy()
        for i in range(3):  # Apply to each channel
            img_smooth[:,:,i] = cv2.bilateralFilter((img_enhanced[:,:,i] * 255).astype(np.uint8), 9, 75, 75) / 255.0

        # Convert back to uint8
        enhanced_img = (np.clip(img_smooth, 0, 1) * 255).astype(np.uint8)

        print("✓ Advanced enhancement completed")
        return enhanced_img

    except Exception as e:
        print(f"⚠ Advanced enhancement failed: {e}")
        return img

def enhance_image_realistic(img):
    """Apply realistic image enhancement for human-like appearance."""
    enhanced = img.copy()
    
    # Step 1: Noise reduction using Non-local Means Denoising
    enhanced = cv2.fastNlMeansDenoisingColored(enhanced, None, 10, 10, 7, 21)
    
    # Step 2: Enhance skin tones and colors
    hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    # Enhance saturation slightly for more vibrant colors
    s = cv2.multiply(s, 1.1)
    s = np.clip(s, 0, 255).astype(np.uint8)
    
    # Enhance value (brightness) with adaptive histogram equalization
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    v = clahe.apply(v)
    
    # Merge back
    enhanced_hsv = cv2.merge([h, s, v])
    enhanced = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
    
    # Step 3: Sharpen the image for better detail
    kernel = np.array([[-1,-1,-1],
                       [-1, 9,-1],
                       [-1,-1,-1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)
    
    # Blend original and sharpened (70% enhanced, 30% sharpened)
    enhanced = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
    
    # Step 4: Bilateral filter for skin smoothing while preserving edges
    enhanced = cv2.bilateralFilter(enhanced, 9, 75, 75)
    
    # Step 5: Final color correction with gamma adjustment
    gamma = 1.2
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    enhanced = cv2.LUT(enhanced, table)
    
    return enhanced

def detect_and_crop_face(img):
    """Detect face and crop to focus on it."""
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # Get the largest face
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face
        
        # Add padding around face
        padding = int(max(w, h) * 0.3)
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(img.shape[1], x + w + padding)
        y2 = min(img.shape[0], y + h + padding)
        
        # Crop the face region
        face_img = img[y1:y2, x1:x2]
        return face_img, True
    
    return img, False

def process_avatar_image(source_image: str, result_name: str = "avatar_image.jpg", 
                        enhance_mode: str = "realistic", target_size: int = 512) -> str:
    """
    Process and enhance a face image for avatar use.
    
    Args:
        source_image: Path to source image
        result_name: Output filename
        enhance_mode: 'advanced', 'realistic', or 'none'
        target_size: Final image size (will be square)
    """
    source_image = str(pathlib.Path(source_image).resolve())
    
    if not os.path.exists(source_image):
        raise FileNotFoundError(f"Source image not found: {source_image}")
    
    print(f"Loading image: {source_image}")
    
    # Load and process the image
    img = cv2.imread(source_image)
    if img is None:
        raise ValueError(f"Could not load image: {source_image}")
    
    print(f"Original image size: {img.shape[1]}x{img.shape[0]}")
    
    # Detect and crop face if possible
    face_img, face_detected = detect_and_crop_face(img)
    
    if face_detected:
        print("Face detected and cropped successfully!")
        img = face_img
    else:
        print("No face detected, using full image.")
    
    # Apply enhancement based on mode
    if enhance_mode == "advanced":
        print("Applying advanced scikit-image enhancement...")
        img = enhance_with_scikit_image(img)
        
    elif enhance_mode == "realistic":
        print("Applying realistic image enhancement...")
        img = enhance_image_realistic(img)
    else:
        print("No enhancement applied.")
    
    # Resize image to target size
    height, width = img.shape[:2]
    print(f"Processing image size: {width}x{height}")
    
    # Create square crop maintaining aspect ratio
    size = min(height, width)
    start_x = (width - size) // 2
    start_y = (height - size) // 2
    img_cropped = img[start_y:start_y+size, start_x:start_x+size]
    
    # Resize to target size with high-quality interpolation
    img_resized = cv2.resize(img_cropped, (target_size, target_size), interpolation=cv2.INTER_LANCZOS4)
    
    # Save the processed image with high quality
    out_path = OUT_DIR / result_name
    
    # Use high quality JPEG settings
    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
    success = cv2.imwrite(str(out_path), img_resized, encode_params)
    
    if success:
        print(f"Avatar image saved to: {out_path}")
        print(f"Final image size: {target_size}x{target_size}")
        return str(out_path)
    else:
        raise RuntimeError(f"Failed to save image to: {out_path}")

def create_avatar_variants(source_image: str, base_name: str = "avatar") -> list:
    """Create multiple variants of the avatar image with different enhancement levels."""
    variants = []
    
    # Advanced enhanced version (most realistic with scikit-image)
    try:
        advanced = process_avatar_image(source_image, f"{base_name}_advanced.jpg", 
                                      enhance_mode="advanced", target_size=512)
        variants.append(("Advanced Enhanced (Most Realistic)", advanced))
    except Exception as e:
        print(f"Advanced enhancement failed: {e}")
    
    # Realistic enhanced version
    try:
        realistic = process_avatar_image(source_image, f"{base_name}_realistic.jpg", 
                                       enhance_mode="realistic", target_size=512)
        variants.append(("Realistic Enhanced", realistic))
    except Exception as e:
        print(f"Realistic enhancement failed: {e}")
    
    # High-resolution realistic version
    try:
        realistic_hd = process_avatar_image(source_image, f"{base_name}_realistic_hd.jpg", 
                                          enhance_mode="realistic", target_size=1024)
        variants.append(("Realistic HD (1024x1024)", realistic_hd))
    except Exception as e:
        print(f"Realistic HD enhancement failed: {e}")
    
    # Simple version without enhancement
    try:
        simple = process_avatar_image(source_image, f"{base_name}_simple.jpg", 
                                    enhance_mode="none", target_size=512)
        variants.append(("Simple", simple))
    except Exception as e:
        print(f"Simple failed: {e}")
    
    return variants

def get_image_info(image_path: str) -> dict:
    """Get basic information about an image file."""
    if not os.path.exists(image_path):
        return {"error": "File not found"}
    
    img = cv2.imread(image_path)
    if img is None:
        return {"error": "Could not load image"}
    
    height, width, channels = img.shape
    file_size = os.path.getsize(image_path)
    
    return {
        "width": width,
        "height": height,
        "channels": channels,
        "file_size_bytes": file_size,
        "file_size_mb": round(file_size / (1024 * 1024), 2)
    }

# ---------- 4) Example usage ----------
if __name__ == "__main__":
    print("=== Advanced Avatar Image Processor ===")
    print("This tool creates ultra-realistic avatar images using advanced image processing.")
    print("Features: Face detection, realistic enhancement, and multiple quality levels")
    print()
    
    # Example usage for image processing
    face_img = "D:\\dk--C\\vdoplus\\sample data\\man face img.jpg"
    
    if os.path.exists(face_img):
        print(f"Found image: {face_img}")
        
        # Get image info
        info = get_image_info(face_img)
        if "error" not in info:
            print(f"Image info: {info['width']}x{info['height']}, {info['file_size_mb']} MB")
        
        print("\nProcessing avatar image with advanced enhancement...")
        try:
            # Create multiple variants using advanced processing
            variants = create_avatar_variants(face_img, "my_avatar")
            
            print(f"\nSuccessfully created {len(variants)} avatar variants:")
            for variant_name, variant_path in variants:
                print(f"  - {variant_name}: {variant_path}")
            
            print(f"\nAll avatars saved in: {OUT_DIR}")
            print("\nRecommended: Use 'Advanced Enhanced' for most realistic human-like results!")
            
        except Exception as e:
            print(f"Error processing avatar: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"Image file not found: {face_img}")
        print("Please update the path to your image file.")
        print("You can modify the 'face_img' variable in the script to point to your image.")
