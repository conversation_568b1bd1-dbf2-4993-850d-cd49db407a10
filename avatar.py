import os
import sys
import subprocess
import shlex
import tempfile
import pathlib
import wave
import numpy as np

# ---------- 0) Small helpers ----------
def pip_install(pkg: str):
    try:
        __import__(pkg.split("==")[0].split("[")[0].replace("-", "_"))
    except Exception:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg])

def run(cmd: str, cwd=None):
    subprocess.check_call(shlex.split(cmd), cwd=cwd)

ROOT = pathlib.Path.cwd()
REPO_DIR = ROOT / "SadTalker"
OUT_DIR  = ROOT / "avatar_outputs"
OUT_DIR.mkdir(exist_ok=True)

# ---------- 1) Dependencies (Python-only) ----------
pip_install("torch")            # You can preinstall a CUDA-matched torch for speed
pip_install("torchvision")
pip_install("numpy")
pip_install("opencv-python")
pip_install("imageio[ffmpeg]")
pip_install("gfpgan")
pip_install("huggingface_hub>=0.23.0")

# Install SadTalker straight from GitHub via pip (keeps things simple)
pip_install("git+https://github.com/OpenTalker/SadTalker.git")

# ---------- 2) Fetch pretrained weights into the expected layout ----------
from huggingface_hub import snapshot_download

ckpt_root = REPO_DIR / "checkpoints"
ckpt_root.mkdir(parents=True, exist_ok=True)

# Main SadTalker checkpoints
snapshot_download(
    repo_id="OpenTalker/SadTalker",
    cache_dir=ckpt_root.as_posix(),
    allow_patterns=[
        "gfpgan/**","sadtalker/**","hubert/**","wav2lip/**","auido2pose/**","auido2exp/**","BFM_Fitting/**","epoch_00190.pth","*.pt","*.pth","*.onnx"
    ],
)

# ---------- 3) Utility: ensure repo files exist ----------
if not REPO_DIR.exists():
    run("git clone --depth 1 https://github.com/OpenTalker/SadTalker.git")
(REPO_DIR / "checkpoints").mkdir(exist_ok=True)
os.environ["SADTALKER_CHECKPOINTS"] = ckpt_root.as_posix()

# ---------- 4) Helper: create silent audio ----------
def create_silent_wav(path: str, duration_sec: float = 3.0, sample_rate: int = 16000):
    """Create a silent WAV file of given duration and sample rate."""
    n_samples = int(duration_sec * sample_rate)
    silence = np.zeros(n_samples, dtype=np.int16)
    with wave.open(path, 'w') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)  # 2 bytes per sample (16-bit)
        wf.setframerate(sample_rate)
        wf.writeframes(silence.tobytes())

# ---------- 5) Core function (no voice, still avatar) ----------
def make_avatar_still(source_image: str, result_name: str = "avatar_still.mp4",
                      enhancer: str = "gfpgan", fps: int = 25) -> str:
    """
    Generate a still avatar video from a face image without voice.
    Internally creates a silent audio file for SadTalker input.
    """
    source_image = str(pathlib.Path(source_image).resolve())

    workdir = tempfile.mkdtemp(prefix="sadtalker_")
    result_dir = pathlib.Path(workdir)
    out_path = OUT_DIR / result_name

    # Create silent audio file
    silent_audio_path = result_dir / "silent.wav"
    create_silent_wav(str(silent_audio_path), duration_sec=3.0, sample_rate=16000)

    # Build SadTalker inference args
    args = [
        sys.executable, "inference.py",
        "--driven_audio", str(silent_audio_path),
        "--source_image", source_image,
        "--result_dir", str(result_dir),
        "--enhancer", enhancer,
        "--fps", str(fps),
        "--still"
    ]

    # Run the official script
    run(" ".join(map(shlex.quote, args)), cwd=REPO_DIR.as_posix())

    # Find the produced mp4 and move to OUT_DIR/result_name
    mp4s = sorted(result_dir.rglob("*.mp4"))
    if not mp4s:
        raise RuntimeError("No result video produced. Check that the face is clear.")
    os.replace(mp4s[0], out_path)
    return out_path.as_posix()

# ---------- 6) Example usage ----------
# face_img = "/path/to/face.jpg"
# out_video = make_avatar_still(face_img, result_name="my_avatar_still.mp4")
# print("Saved:", out_video)