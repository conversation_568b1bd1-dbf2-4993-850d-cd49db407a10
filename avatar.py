import os
from io import BytesIO
from PIL import Image

from google import genai
from google.genai import types

# --- CONFIG ---
API_KEY = "AIzaSyBS4AhVXV7eteES608E4j2xdF9UL9rUtgA"
INPUT_PHOTO = r"D:\dk--C\vdoplus\sample data\WhatsApp Image 2024-04-04 at 11.34.04_47ff079f.jpg"
OUTPUT_PHOTO = r"D:\dk--C\vdoplus\avatar_from_photo.jpg"

# --- CLIENT ---
client = genai.Client(api_key=API_KEY)

def enhance_avatar_from_photo(input_photo_path: str, out_path: str, aspect_ratio: str = "3:4"):
    """
    Enhance a real portrait photo into a polished avatar.
    Uses the generate_image API which supports direct image outputs.
    """
    with open(input_photo_path, "rb") as f:
        src_bytes = f.read()

    prompt = (
        "Enhance this portrait photo into a professional avatar while preserving identity. "
        "Keep natural skin tone and facial structure; do not change age or features. "
        "Apply balanced lighting, gentle skin cleanup (not over-smoothed), and a soft background. "
        "Crop to head-and-shoulders portrait. No text, no watermarks, no logos."
    )

    result = client.models.generate_image(
        model="gemini-2.0-flash",   # or "imagen-3.0-generate-002" if you want photorealistic
        prompt=prompt,
        image=src_bytes,
        config=types.GenerateImageConfig(
            number_of_images=1,
            output_mime_type="image/jpeg",
            aspect_ratio=aspect_ratio
        )
    )

    img_bytes = result.generated_images[0].image.image_bytes
    Image.open(BytesIO(img_bytes)).save(out_path, "JPEG", quality=95)
    return out_path


if __name__ == "__main__":
    out = enhance_avatar_from_photo(INPUT_PHOTO, OUTPUT_PHOTO)
    print("✅ Avatar saved at:", out)
