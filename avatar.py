import os
import sys
import subprocess
import shlex
import tempfile
import pathlib
import wave
import numpy as np
from PIL import Image
import cv2

# ---------- 0) Small helpers ----------
def pip_install(pkg: str):
    try:
        __import__(pkg.split("==")[0].split("[")[0].replace("-", "_"))
    except Exception:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg])

def run(cmd: str, cwd=None):
    subprocess.check_call(shlex.split(cmd), cwd=cwd)

ROOT = pathlib.Path.cwd()
REPO_DIR = ROOT / "SadTalker"
OUT_DIR  = ROOT / "avatar_outputs"
OUT_DIR.mkdir(exist_ok=True)

# ---------- 1) Dependencies (Python-only) ----------
pip_install("torch")            # You can preinstall a CUDA-matched torch for speed
pip_install("torchvision")
pip_install("numpy")
pip_install("opencv-python")
pip_install("imageio[ffmpeg]")
pip_install("Pillow")
pip_install("scikit-image")

# Clone SadTalker repository instead of installing via pip
if not REPO_DIR.exists():
    print("Cloning SadTalker repository...")
    run("git clone --depth 1 https://github.com/OpenTalker/SadTalker.git")
    print("Repository cloned successfully!")

# ---------- 2) Setup for advanced image processing ----------
print("Setting up advanced image processing for realistic avatars...")

# Check for additional libraries
try:
    from skimage import restoration, filters, exposure
    print("✓ Scikit-image loaded successfully")
    SKIMAGE_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Scikit-image not available: {e}")
    SKIMAGE_AVAILABLE = False

print("Avatar processing ready with advanced enhancement!")

# ---------- 3) AI-powered face enhancement functions ----------
def download_model_if_needed(url, local_path):
    """Download model file if it doesn't exist locally."""
    import urllib.request
    import os

    if os.path.exists(local_path):
        return local_path

    print(f"Downloading model from {url}...")
    os.makedirs(os.path.dirname(local_path), exist_ok=True)

    try:
        urllib.request.urlretrieve(url, local_path)
        print(f"✓ Model downloaded to {local_path}")
        return local_path
    except Exception as e:
        print(f"⚠ Failed to download model: {e}")
        return None

def initialize_gfpgan(upscale=2, arch='clean', channel_multiplier=2):
    """Initialize GFPGAN model for face restoration."""
    if not GFPGAN_AVAILABLE:
        return None

    try:
        # Create models directory
        models_dir = ROOT / "models"
        models_dir.mkdir(exist_ok=True)

        # GFPGAN model path
        model_url = 'https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth'
        model_path = models_dir / "GFPGANv1.3.pth"

        # Download model if needed
        downloaded_path = download_model_if_needed(model_url, str(model_path))
        if not downloaded_path:
            return None

        restorer = GFPGANer(
            model_path=downloaded_path,
            upscale=upscale,
            arch=arch,
            channel_multiplier=channel_multiplier,
            bg_upsampler=None  # We'll handle background separately
        )
        print(f"✓ GFPGAN initialized with {upscale}x upscaling")
        return restorer
    except Exception as e:
        print(f"⚠ Failed to initialize GFPGAN: {e}")
        return None

def initialize_realesrgan(scale=4):
    """Initialize Real-ESRGAN model for super-resolution."""
    if not REALESRGAN_AVAILABLE:
        return None

    try:
        # Create models directory
        models_dir = ROOT / "models"
        models_dir.mkdir(exist_ok=True)

        # Real-ESRGAN model
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=scale)
        model_url = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth'
        model_path = models_dir / "RealESRGAN_x4plus.pth"

        # Download model if needed
        downloaded_path = download_model_if_needed(model_url, str(model_path))
        if not downloaded_path:
            return None

        upsampler = RealESRGANer(
            scale=scale,
            model_path=downloaded_path,
            model=model,
            tile=0,
            tile_pad=10,
            pre_pad=0,
            half=False  # Set to True if you have a powerful GPU
        )
        print(f"✓ Real-ESRGAN initialized with {scale}x upscaling")
        return upsampler
    except Exception as e:
        print(f"⚠ Failed to initialize Real-ESRGAN: {e}")
        return None

def enhance_face_with_ai(img, gfpgan_restorer=None, realesrgan_upsampler=None):
    """Apply AI-powered face enhancement using GFPGAN and Real-ESRGAN."""
    enhanced_img = img.copy()

    # Step 1: Face restoration with GFPGAN
    if gfpgan_restorer is not None:
        try:
            print("Applying GFPGAN face restoration...")
            # GFPGAN expects RGB format
            img_rgb = cv2.cvtColor(enhanced_img, cv2.COLOR_BGR2RGB)

            # Apply GFPGAN
            _, _, enhanced_img = gfpgan_restorer.enhance(
                img_rgb,
                has_aligned=False,
                only_center_face=False,
                paste_back=True,
                weight=0.5  # Blend with original (0.5 = 50% blend)
            )

            # Convert back to BGR
            enhanced_img = cv2.cvtColor(enhanced_img, cv2.COLOR_RGB2BGR)
            print("✓ GFPGAN face restoration completed")

        except Exception as e:
            print(f"⚠ GFPGAN enhancement failed: {e}")

    # Step 2: Super-resolution with Real-ESRGAN (optional, for very high quality)
    if realesrgan_upsampler is not None:
        try:
            print("Applying Real-ESRGAN super-resolution...")
            enhanced_img, _ = realesrgan_upsampler.enhance(enhanced_img, outscale=2)
            print("✓ Real-ESRGAN super-resolution completed")
        except Exception as e:
            print(f"⚠ Real-ESRGAN enhancement failed: {e}")

    return enhanced_img

def enhance_image_quality_basic(img):
    """Apply basic image enhancement techniques as fallback."""
    # Convert to LAB color space for better processing
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    l = clahe.apply(l)

    # Merge channels and convert back to BGR
    enhanced = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)

    # Apply slight gaussian blur to smooth skin
    enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)

    return enhanced

def enhance_image_realistic(img):
    """Apply advanced image enhancement for more realistic appearance."""
    enhanced = img.copy()

    # Step 1: Noise reduction using Non-local Means Denoising
    enhanced = cv2.fastNlMeansDenoisingColored(enhanced, None, 10, 10, 7, 21)

    # Step 2: Enhance skin tones and colors
    # Convert to HSV for better color manipulation
    hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)

    # Enhance saturation slightly for more vibrant colors
    s = cv2.multiply(s, 1.1)
    s = np.clip(s, 0, 255).astype(np.uint8)

    # Enhance value (brightness) with adaptive histogram equalization
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    v = clahe.apply(v)

    # Merge back
    enhanced_hsv = cv2.merge([h, s, v])
    enhanced = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)

    # Step 3: Sharpen the image for better detail
    kernel = np.array([[-1,-1,-1],
                       [-1, 9,-1],
                       [-1,-1,-1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # Blend original and sharpened (50% each)
    enhanced = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)

    # Step 4: Bilateral filter for skin smoothing while preserving edges
    enhanced = cv2.bilateralFilter(enhanced, 9, 75, 75)

    # Step 5: Final color correction
    # Adjust gamma for better contrast
    gamma = 1.2
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    enhanced = cv2.LUT(enhanced, table)

    return enhanced

def detect_and_crop_face(img):
    """Detect face and crop to focus on it."""
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)

    if len(faces) > 0:
        # Get the largest face
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face

        # Add padding around face
        padding = int(max(w, h) * 0.3)
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(img.shape[1], x + w + padding)
        y2 = min(img.shape[0], y + h + padding)

        # Crop the face region
        face_img = img[y1:y2, x1:x2]
        return face_img, True

    return img, False

# ---------- 4) Enhanced avatar image processing with AI ----------
def process_avatar_image(source_image: str, result_name: str = "avatar_image.jpg",
                        enhance_mode: str = "ai", target_size: int = 512) -> str:
    """
    Process and enhance a face image for avatar use with AI enhancement.

    Args:
        source_image: Path to source image
        result_name: Output filename
        enhance_mode: 'ai' for AI enhancement, 'basic' for basic enhancement, 'none' for no enhancement
        target_size: Final image size (will be square)
    """
    source_image = str(pathlib.Path(source_image).resolve())

    if not os.path.exists(source_image):
        raise FileNotFoundError(f"Source image not found: {source_image}")

    print(f"Loading image: {source_image}")

    # Load and process the image
    img = cv2.imread(source_image)
    if img is None:
        raise ValueError(f"Could not load image: {source_image}")

    print(f"Original image size: {img.shape[1]}x{img.shape[0]}")

    # Detect and crop face if possible
    face_img, face_detected = detect_and_crop_face(img)

    if face_detected:
        print("Face detected and cropped successfully!")
        img = face_img
    else:
        print("No face detected, using full image.")

    # Apply enhancement based on mode
    if enhance_mode == "ai":
        print("Applying AI-powered face enhancement...")
        # Try AI enhancement first, fallback to realistic enhancement
        gfpgan_restorer = initialize_gfpgan(upscale=2)
        realesrgan_upsampler = initialize_realesrgan(scale=2) if target_size > 512 else None

        if gfpgan_restorer or realesrgan_upsampler:
            img = enhance_face_with_ai(img, gfpgan_restorer, realesrgan_upsampler)
        else:
            print("AI models not available, using realistic enhancement...")
            img = enhance_image_realistic(img)

    elif enhance_mode == "realistic":
        print("Applying realistic image enhancement...")
        img = enhance_image_realistic(img)

    elif enhance_mode == "basic":
        print("Applying basic image enhancement...")
        img = enhance_image_quality_basic(img)
    else:
        print("No enhancement applied.")

    # Resize image to target size
    height, width = img.shape[:2]
    print(f"Processing image size: {width}x{height}")

    # Create square crop maintaining aspect ratio
    size = min(height, width)
    start_x = (width - size) // 2
    start_y = (height - size) // 2
    img_cropped = img[start_y:start_y+size, start_x:start_x+size]

    # Resize to target size with high-quality interpolation
    img_resized = cv2.resize(img_cropped, (target_size, target_size), interpolation=cv2.INTER_LANCZOS4)

    # Save the processed image with high quality
    out_path = OUT_DIR / result_name

    # Use high quality JPEG settings
    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
    success = cv2.imwrite(str(out_path), img_resized, encode_params)

    if success:
        print(f"Avatar image saved to: {out_path}")
        print(f"Final image size: {target_size}x{target_size}")
        return str(out_path)
    else:
        raise RuntimeError(f"Failed to save image to: {out_path}")

# ---------- 5) Enhanced utility functions ----------
def create_avatar_variants(source_image: str, base_name: str = "avatar") -> list:
    """
    Create multiple variants of the avatar image with different enhancement levels.
    """
    variants = []

    # Realistic enhanced version (most human-like)
    try:
        realistic = process_avatar_image(source_image, f"{base_name}_realistic.jpg",
                                       enhance_mode="realistic", target_size=512)
        variants.append(("Realistic Enhanced (Most Human-like)", realistic))
    except Exception as e:
        print(f"Realistic enhancement failed: {e}")

    # High-resolution realistic version
    try:
        realistic_hd = process_avatar_image(source_image, f"{base_name}_realistic_hd.jpg",
                                          enhance_mode="realistic", target_size=1024)
        variants.append(("Realistic HD (1024x1024)", realistic_hd))
    except Exception as e:
        print(f"Realistic HD enhancement failed: {e}")

    # AI-enhanced version (if available)
    try:
        ai_enhanced = process_avatar_image(source_image, f"{base_name}_ai_enhanced.jpg",
                                         enhance_mode="ai", target_size=512)
        variants.append(("AI Enhanced", ai_enhanced))
    except Exception as e:
        print(f"AI enhancement failed: {e}")

    # Basic enhanced version
    basic = process_avatar_image(source_image, f"{base_name}_basic.jpg",
                               enhance_mode="basic", target_size=512)
    variants.append(("Basic Enhanced", basic))

    # Simple version without enhancement
    simple = process_avatar_image(source_image, f"{base_name}_simple.jpg",
                                enhance_mode="none", target_size=512)
    variants.append(("Simple", simple))

    return variants

def get_image_info(image_path: str) -> dict:
    """Get basic information about an image file."""
    if not os.path.exists(image_path):
        return {"error": "File not found"}

    img = cv2.imread(image_path)
    if img is None:
        return {"error": "Could not load image"}

    height, width, channels = img.shape
    file_size = os.path.getsize(image_path)

    return {
        "width": width,
        "height": height,
        "channels": channels,
        "file_size_bytes": file_size,
        "file_size_mb": round(file_size / (1024 * 1024), 2)
    }

# ---------- 6) Example usage ----------
if __name__ == "__main__":
    print("=== AI-Powered Avatar Image Processor ===")
    print("This tool uses GFPGAN and Real-ESRGAN to create realistic avatar images.")
    print("Features: Face restoration, super-resolution, and quality enhancement")
    print()

    # Example usage for image processing
    face_img = "D:\\dk--C\\vdoplus\\sample data\\real human .jpg"

    if os.path.exists(face_img):
        print(f"Found image: {face_img}")

        # Get image info
        info = get_image_info(face_img)
        if "error" not in info:
            print(f"Image info: {info['width']}x{info['height']}, {info['file_size_mb']} MB")

        print("\nProcessing avatar image...")
        try:
            # Create multiple variants
            variants = create_avatar_variants(face_img, "my_avatar")

            print(f"\nSuccessfully created {len(variants)} avatar variants:")
            for variant_name, variant_path in variants:
                print(f"  - {variant_name}: {variant_path}")

            print(f"\nAll avatars saved in: {OUT_DIR}")

        except Exception as e:
            print(f"Error processing avatar: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"Image file not found: {face_img}")
        print("Please update the path to your image file.")
        print("You can modify the 'face_img' variable in the script to point to your image.")