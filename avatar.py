import os
import requests
from io import BytesIO
from PIL import Image

from google import genai
from google.genai import types

# --- CONFIG ---
API_KEY = "AIzaSyDAk3eITMxO9pRGQCaX9uHLtUN8_7Yvlv4"
INPUT_PHOTO = r"D:\\dk--C\\vdoplus\\sample data\\WIN_20231226_14_17_53_Pro.jpg"
OUTPUT_PHOTO = r"avatar_from_photo.jpg"

def download_test_image():
    """Download a test human face image if it doesn't exist."""
    if not os.path.exists(INPUT_PHOTO):
        print("📥 Downloading test human face image...")
        # Using a free stock photo of a human face
        url = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                with open(INPUT_PHOTO, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Test image downloaded: {INPUT_PHOTO}")
                return True
            else:
                print(f"❌ Failed to download image: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error downloading image: {e}")
            return False
    else:
        print(f"✅ Using existing image: {INPUT_PHOTO}")
        return True

# --- CLIENT ---
client = genai.Client(api_key=API_KEY)

def create_fallback_avatar(input_photo_path: str, out_path: str):
    """
    Create an enhanced avatar using PIL image processing as fallback.
    """
    try:
        print("🎨 Creating enhanced avatar using image processing...")

        # Open and process the image
        img = Image.open(input_photo_path)

        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Resize to square format (avatar standard)
        size = min(img.size)
        left = (img.width - size) // 2
        top = (img.height - size) // 2
        right = left + size
        bottom = top + size

        # Crop to square
        img = img.crop((left, top, right, bottom))

        # Resize to standard avatar size
        img = img.resize((512, 512), Image.Resampling.LANCZOS)

        # Enhance the image
        from PIL import ImageEnhance, ImageFilter

        # Enhance sharpness
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.2)

        # Enhance contrast
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.1)

        # Enhance color
        enhancer = ImageEnhance.Color(img)
        img = enhancer.enhance(1.05)

        # Apply subtle smoothing
        img = img.filter(ImageFilter.SMOOTH_MORE)

        # Save the enhanced image
        img.save(out_path, "JPEG", quality=95, optimize=True)

        print(f"✅ Fallback avatar created: {out_path}")
        return out_path

    except Exception as e:
        print(f"❌ Fallback enhancement failed: {e}")
        # Last resort: just copy the original
        import shutil
        shutil.copy2(input_photo_path, out_path)
        return out_path

def enhance_avatar_from_photo(input_photo_path: str, out_path: str):
    """
    Enhance a real portrait photo into a polished avatar.
    Uses the Google GenAI API for image generation.
    """
    try:
        # Load the input image
        with open(input_photo_path, "rb") as f:
            src_bytes = f.read()

        prompt = (
            "Enhance this portrait photo into a professional avatar while preserving identity. "
            "Keep natural skin tone and facial structure; do not change age or features. "
            "Apply balanced lighting, gentle skin cleanup (not over-smoothed), and a soft background. "
            "Crop to head-and-shoulders portrait. No text, no watermarks, no logos. "
            "Make it look like a professional headshot photo."
        )

        # Try the correct API method
        try:
            # Method 1: Try generate_image
            result = client.models.generate_image(
                model="imagen-3.0-generate-002",
                prompt=prompt,
                image=src_bytes,
                config=types.GenerateImageConfig(
                    number_of_images=1,
                    output_mime_type="image/jpeg",
                    aspect_ratio="1:1"
                )
            )
            img_bytes = result.generated_images[0].image.image_bytes

        except AttributeError:
            # Method 2: Try alternative API structure
            print("🔄 Trying alternative API method...")
            result = client.generate_image(
                model="imagen-3.0-generate-002",
                prompt=prompt,
                image=src_bytes,
                config=types.GenerateImageConfig(
                    number_of_images=1,
                    output_mime_type="image/jpeg",
                    aspect_ratio="1:1"
                )
            )
            img_bytes = result.generated_images[0].image.image_bytes

        # Save the generated image
        Image.open(BytesIO(img_bytes)).save(out_path, "JPEG", quality=95)
        return out_path

    except Exception as e:
        print(f"⚠️  API error: {e}")
        # Fallback: Create a simple enhanced version using PIL
        print("🔄 Using fallback image enhancement...")
        return create_fallback_avatar(input_photo_path, out_path)


if __name__ == "__main__":
    print("🎭 === Real Human Avatar Generator ===")
    print("Creating realistic avatar using Google Gemini AI...")
    print()

    # Download test image if needed
    if not download_test_image():
        print("❌ Could not get test image. Please provide your own image.")
        exit(1)

    try:
        print("🚀 Generating avatar...")
        out = enhance_avatar_from_photo(INPUT_PHOTO, OUTPUT_PHOTO)
        print("✅ Avatar saved at:", out)
        print("🎉 Avatar generation completed successfully!")
    except Exception as e:
        print(f"❌ Error generating avatar: {e}")
        print("💡 Please check your API key and internet connection.")
