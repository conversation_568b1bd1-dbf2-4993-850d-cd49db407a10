import os
import sys
import subprocess
import shlex
import tempfile
import pathlib
import wave
import numpy as np
from PIL import Image
import cv2

# ---------- 0) Small helpers ----------
def pip_install(pkg: str):
    try:
        __import__(pkg.split("==")[0].split("[")[0].replace("-", "_"))
    except Exception:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pkg])

def run(cmd: str, cwd=None):
    subprocess.check_call(shlex.split(cmd), cwd=cwd)

ROOT = pathlib.Path.cwd()
REPO_DIR = ROOT / "SadTalker"
OUT_DIR  = ROOT / "avatar_outputs"
OUT_DIR.mkdir(exist_ok=True)

# ---------- 1) Dependencies (Python-only) ----------
pip_install("torch")            # You can preinstall a CUDA-matched torch for speed
pip_install("torchvision")
pip_install("numpy")
pip_install("opencv-python")
pip_install("imageio[ffmpeg]")
pip_install("gfpgan")
pip_install("huggingface_hub>=0.23.0")
pip_install("Pillow")

# Clone SadTalker repository instead of installing via pip
if not REPO_DIR.exists():
    print("Cloning SadTalker repository...")
    run("git clone --depth 1 https://github.com/OpenTalker/SadTalker.git")
    print("Repository cloned successfully!")

# ---------- 2) Setup for image processing ----------
# We'll focus on basic image processing for now
# SadTalker model download can be added later if needed

print("Setting up image processing environment...")
print("Avatar processing ready!")

# ---------- 3) Enhanced image processing functions ----------
def enhance_image_quality(img):
    """Apply basic image enhancement techniques."""
    # Convert to LAB color space for better processing
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    l = clahe.apply(l)

    # Merge channels and convert back to BGR
    enhanced = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)

    # Apply slight gaussian blur to smooth skin
    enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)

    return enhanced

def detect_and_crop_face(img):
    """Detect face and crop to focus on it."""
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)

    if len(faces) > 0:
        # Get the largest face
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face

        # Add padding around face
        padding = int(max(w, h) * 0.3)
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(img.shape[1], x + w + padding)
        y2 = min(img.shape[0], y + h + padding)

        # Crop the face region
        face_img = img[y1:y2, x1:x2]
        return face_img, True

    return img, False

# ---------- 4) Enhanced avatar image processing ----------
def process_avatar_image(source_image: str, result_name: str = "avatar_image.jpg", enhance: bool = True) -> str:
    """
    Process and enhance a face image for avatar use.
    This approach focuses on image quality and face detection.
    """
    source_image = str(pathlib.Path(source_image).resolve())

    if not os.path.exists(source_image):
        raise FileNotFoundError(f"Source image not found: {source_image}")

    print(f"Loading image: {source_image}")

    # Load and process the image
    img = cv2.imread(source_image)
    if img is None:
        raise ValueError(f"Could not load image: {source_image}")

    print(f"Original image size: {img.shape[1]}x{img.shape[0]}")

    # Detect and crop face if possible
    face_img, face_detected = detect_and_crop_face(img)

    if face_detected:
        print("Face detected and cropped successfully!")
        img = face_img
    else:
        print("No face detected, using full image.")

    # Enhance image quality if requested
    if enhance:
        print("Enhancing image quality...")
        img = enhance_image_quality(img)

    # Resize image to standard avatar size (512x512)
    height, width = img.shape[:2]
    print(f"Processing image size: {width}x{height}")

    # Create square crop maintaining aspect ratio
    size = min(height, width)
    start_x = (width - size) // 2
    start_y = (height - size) // 2
    img_cropped = img[start_y:start_y+size, start_x:start_x+size]

    # Resize to 512x512
    img_resized = cv2.resize(img_cropped, (512, 512), interpolation=cv2.INTER_LANCZOS4)

    # Save the processed image
    out_path = OUT_DIR / result_name
    success = cv2.imwrite(str(out_path), img_resized)

    if success:
        print(f"Avatar image saved to: {out_path}")
        print(f"Final image size: 512x512")
        return str(out_path)
    else:
        raise RuntimeError(f"Failed to save image to: {out_path}")

# ---------- 5) Additional utility functions ----------
def create_avatar_variants(source_image: str, base_name: str = "avatar") -> list:
    """
    Create multiple variants of the avatar image with different processing options.
    """
    variants = []

    # Original processed version
    original = process_avatar_image(source_image, f"{base_name}_enhanced.jpg", enhance=True)
    variants.append(("Enhanced", original))

    # Simple version without enhancement
    simple = process_avatar_image(source_image, f"{base_name}_simple.jpg", enhance=False)
    variants.append(("Simple", simple))

    return variants

def get_image_info(image_path: str) -> dict:
    """Get basic information about an image file."""
    if not os.path.exists(image_path):
        return {"error": "File not found"}

    img = cv2.imread(image_path)
    if img is None:
        return {"error": "Could not load image"}

    height, width, channels = img.shape
    file_size = os.path.getsize(image_path)

    return {
        "width": width,
        "height": height,
        "channels": channels,
        "file_size_bytes": file_size,
        "file_size_mb": round(file_size / (1024 * 1024), 2)
    }

# ---------- 6) Example usage ----------
if __name__ == "__main__":
    print("=== Avatar Image Processor ===")
    print("This tool processes face images to create avatar images.")
    print()

    # Example usage for image processing
    face_img = "D:\\dk--C\\vdoplus\\sample data\\man face img.jpg"

    if os.path.exists(face_img):
        print(f"Found image: {face_img}")

        # Get image info
        info = get_image_info(face_img)
        if "error" not in info:
            print(f"Image info: {info['width']}x{info['height']}, {info['file_size_mb']} MB")

        print("\nProcessing avatar image...")
        try:
            # Create multiple variants
            variants = create_avatar_variants(face_img, "my_avatar")

            print(f"\nSuccessfully created {len(variants)} avatar variants:")
            for variant_name, variant_path in variants:
                print(f"  - {variant_name}: {variant_path}")

            print(f"\nAll avatars saved in: {OUT_DIR}")

        except Exception as e:
            print(f"Error processing avatar: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"Image file not found: {face_img}")
        print("Please update the path to your image file.")
        print("You can modify the 'face_img' variable in the script to point to your image.")