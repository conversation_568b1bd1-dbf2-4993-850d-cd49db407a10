"""
Enhanced Real Human Avatar Generator
Creates ultra-realistic avatars from human photos using advanced image processing.
"""

import os
import requests
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageFilter
from io import BytesIO

# Configuration
INPUT_PHOTO = "test_human_face.jpg"
OUTPUT_PHOTO = "enhanced_realistic_avatar.jpg"

def download_test_image():
    """Download a test human face image if it doesn't exist."""
    if not os.path.exists(INPUT_PHOTO):
        print("📥 Downloading test human face image...")
        url = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                with open(INPUT_PHOTO, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Test image downloaded: {INPUT_PHOTO}")
                return True
            else:
                print(f"❌ Failed to download image: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error downloading image: {e}")
            return False
    else:
        print(f"✅ Using existing image: {INPUT_PHOTO}")
        return True

def detect_and_crop_face(image):
    """Detect and crop face using OpenCV."""
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    if len(faces) > 0:
        # Get the largest face
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face
        
        # Add padding around face
        padding = int(max(w, h) * 0.3)
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(image.shape[1], x + w + padding)
        y2 = min(image.shape[0], y + h + padding)
        
        face_img = image[y1:y2, x1:x2]
        return face_img, True
    
    return image, False

def enhance_for_realism(image):
    """Apply comprehensive enhancement for ultra-realistic appearance."""
    # Advanced noise reduction
    image = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
    
    # Skin tone enhancement
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    # Enhance saturation for natural colors
    s = cv2.multiply(s, 1.1)
    s = np.clip(s, 0, 255).astype(np.uint8)
    
    # Adaptive brightness enhancement
    clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
    v = clahe.apply(v)
    
    enhanced_hsv = cv2.merge([h, s, v])
    image = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
    
    # Professional sharpening
    kernel = np.array([[-1,-1,-1], [-1, 9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(image, -1, kernel)
    image = cv2.addWeighted(image, 0.8, sharpened, 0.2, 0)
    
    # Skin smoothing while preserving details
    image = cv2.bilateralFilter(image, 9, 75, 75)
    
    # Color grading in LAB space
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    clahe_lab = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    l = clahe_lab.apply(l)
    
    enhanced_lab = cv2.merge([l, a, b])
    image = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    
    # Final gamma correction
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in np.arange(0, 256)]).astype("uint8")
    image = cv2.LUT(image, table)
    
    return image

def create_professional_avatar(input_photo_path: str, output_path: str):
    """Create a professional avatar from a human photo."""
    print(f"📸 Loading image: {input_photo_path}")
    
    # Load image
    image = cv2.imread(input_photo_path)
    if image is None:
        raise ValueError(f"Could not load image: {input_photo_path}")
    
    print(f"📏 Original size: {image.shape[1]}x{image.shape[0]}")
    
    # Detect and crop face
    face_img, face_detected = detect_and_crop_face(image)
    
    if face_detected:
        print("👤 Face detected and cropped successfully!")
        image = face_img
    else:
        print("⚠️  No face detected, using full image")
    
    # Apply realistic enhancement
    print("✨ Applying realistic enhancement...")
    enhanced_image = enhance_for_realism(image)
    
    # Create square crop
    height, width = enhanced_image.shape[:2]
    crop_size = min(height, width)
    start_x = (width - crop_size) // 2
    start_y = (height - crop_size) // 2
    cropped = enhanced_image[start_y:start_y+crop_size, start_x:start_x+crop_size]
    
    # Resize to standard avatar size
    final_image = cv2.resize(cropped, (512, 512), interpolation=cv2.INTER_LANCZOS4)
    
    # Convert to PIL for final enhancements
    pil_image = Image.fromarray(cv2.cvtColor(final_image, cv2.COLOR_BGR2RGB))
    
    # Final PIL enhancements
    enhancer = ImageEnhance.Sharpness(pil_image)
    pil_image = enhancer.enhance(1.1)
    
    enhancer = ImageEnhance.Contrast(pil_image)
    pil_image = enhancer.enhance(1.05)
    
    # Apply subtle smoothing
    pil_image = pil_image.filter(ImageFilter.SMOOTH_MORE)
    
    # Save with high quality
    pil_image.save(output_path, "JPEG", quality=98, optimize=True)
    
    print(f"💾 Professional avatar saved: {output_path}")
    print(f"📐 Final size: 512x512")
    
    return output_path

def create_multiple_avatar_styles(input_photo_path: str):
    """Create multiple avatar styles from one input photo."""
    styles = [
        ("professional", "Professional headshot style"),
        ("natural", "Natural enhanced style"),
        ("artistic", "Artistic interpretation"),
        ("social_media", "Social media optimized")
    ]
    
    results = []
    
    for style_name, description in styles:
        try:
            print(f"\n--- Creating {description} ---")
            output_name = f"avatar_{style_name}.jpg"
            
            # Load and process image
            image = cv2.imread(input_photo_path)
            face_img, face_detected = detect_and_crop_face(image)
            
            if face_detected:
                image = face_img
            
            # Apply style-specific enhancements
            if style_name == "professional":
                enhanced = enhance_for_realism(image)
            elif style_name == "natural":
                enhanced = cv2.bilateralFilter(image, 9, 80, 80)
            elif style_name == "artistic":
                enhanced = enhance_for_realism(image)
                # Add artistic color enhancement
                hsv = cv2.cvtColor(enhanced, cv2.COLOR_BGR2HSV)
                h, s, v = cv2.split(hsv)
                s = cv2.multiply(s, 1.3)  # More vibrant colors
                s = np.clip(s, 0, 255).astype(np.uint8)
                enhanced = cv2.cvtColor(cv2.merge([h, s, v]), cv2.COLOR_HSV2BGR)
            else:  # social_media
                enhanced = enhance_for_realism(image)
                # Brighten for social media
                enhanced = cv2.convertScaleAbs(enhanced, alpha=1.1, beta=10)
            
            # Create square crop and resize
            height, width = enhanced.shape[:2]
            crop_size = min(height, width)
            start_x = (width - crop_size) // 2
            start_y = (height - crop_size) // 2
            cropped = enhanced[start_y:start_y+crop_size, start_x:start_x+crop_size]
            final_image = cv2.resize(cropped, (512, 512), interpolation=cv2.INTER_LANCZOS4)
            
            # Save
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 98]
            cv2.imwrite(output_name, final_image, encode_params)
            
            results.append((style_name, output_name))
            print(f"✅ {description} completed: {output_name}")
            
        except Exception as e:
            print(f"❌ Failed to create {style_name} avatar: {e}")
    
    return results

def main():
    """Main function to create realistic human avatars."""
    print("🎭 === Enhanced Real Human Avatar Generator ===")
    print("Creating ultra-realistic avatars using advanced image processing")
    print()
    
    # Download test image if needed
    if not download_test_image():
        print("❌ Could not get test image. Please provide your own image.")
        return
    
    try:
        # Create single professional avatar
        print("🚀 Creating professional avatar...")
        avatar_path = create_professional_avatar(INPUT_PHOTO, OUTPUT_PHOTO)
        print(f"✅ Professional avatar created: {avatar_path}")
        
        # Create multiple styles
        print("\n🎨 Creating multiple avatar styles...")
        results = create_multiple_avatar_styles(INPUT_PHOTO)
        
        print(f"\n🎉 Avatar generation completed!")
        print(f"📂 Created {len(results) + 1} avatar variations:")
        print(f"  - Professional: {OUTPUT_PHOTO}")
        for style_name, output_name in results:
            print(f"  - {style_name.title()}: {output_name}")
        
    except Exception as e:
        print(f"❌ Error generating avatars: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
