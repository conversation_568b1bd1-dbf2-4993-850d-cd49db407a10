"""
Ultra-Realistic Human Avatar Generator
Creates the most realistic avatars using advanced computer vision and image processing.
Optimized for maximum realism and professional quality.
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import requests

# Configuration
INPUT_PHOTO = r"D:\\dk--C\\vdoplus\\sample data\\women face img.jpg"
OUTPUT_PHOTO = r"ultra_realistic_avatar.jpg"

def detect_face_landmarks(image):
    """Detect face and key landmarks for precise processing."""
    # Load face detection cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
    
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)
    
    face_info = None
    if len(faces) > 0:
        # Get the largest face
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face
        
        # Detect eyes within the face region
        face_roi = gray[y:y+h, x:x+w]
        eyes = eye_cascade.detectMultiScale(face_roi)
        
        face_info = {
            'face': (x, y, w, h),
            'eyes': eyes,
            'center': (x + w//2, y + h//2)
        }
    
    return face_info

def enhance_skin_texture(image, face_region=None):
    """Apply advanced skin enhancement while preserving natural texture."""
    # Convert to LAB color space for better skin tone control
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # Apply CLAHE to L channel for better lighting
    clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
    l = clahe.apply(l)
    
    # Enhance skin tones in A and B channels
    a = cv2.multiply(a, 1.05)  # Slight red enhancement
    b = cv2.multiply(b, 0.98)  # Slight yellow reduction
    
    # Merge back
    enhanced_lab = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    
    # Apply bilateral filter for skin smoothing while preserving edges
    smoothed = cv2.bilateralFilter(enhanced, 15, 80, 80)
    
    # Blend original and smoothed for natural look
    result = cv2.addWeighted(enhanced, 0.7, smoothed, 0.3, 0)
    
    return result

def apply_professional_lighting(image):
    """Simulate professional studio lighting."""
    # Convert to HSV for better control
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    # Create a subtle vignette effect
    rows, cols = v.shape
    kernel_x = cv2.getGaussianKernel(cols, cols/3)
    kernel_y = cv2.getGaussianKernel(rows, rows/3)
    kernel = kernel_y * kernel_x.T
    mask = kernel / kernel.max()
    
    # Apply lighting enhancement
    v_enhanced = v.astype(np.float32)
    v_enhanced = v_enhanced * (0.8 + 0.4 * mask)
    v_enhanced = np.clip(v_enhanced, 0, 255).astype(np.uint8)
    
    # Enhance saturation slightly
    s_enhanced = cv2.multiply(s, 1.1)
    s_enhanced = np.clip(s_enhanced, 0, 255).astype(np.uint8)
    
    # Merge back
    enhanced_hsv = cv2.merge([h, s_enhanced, v_enhanced])
    result = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
    
    return result

def sharpen_details(image, strength=1.2):
    """Apply professional sharpening for crisp details."""
    # Create sharpening kernel
    kernel = np.array([[-1,-1,-1],
                       [-1, 9,-1],
                       [-1,-1,-1]]) * strength
    
    # Apply sharpening
    sharpened = cv2.filter2D(image, -1, kernel)
    
    # Blend with original for natural look
    result = cv2.addWeighted(image, 0.7, sharpened, 0.3, 0)
    
    return result

def reduce_noise_advanced(image):
    """Apply advanced noise reduction while preserving details."""
    # Apply Non-local Means Denoising
    denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
    
    # Apply additional Gaussian blur for very subtle smoothing
    blurred = cv2.GaussianBlur(denoised, (3, 3), 0.5)
    
    # Blend for natural result
    result = cv2.addWeighted(denoised, 0.8, blurred, 0.2, 0)
    
    return result

def create_ultra_realistic_avatar(input_path, output_path):
    """Create the most realistic avatar possible."""
    print("🎭 === Ultra-Realistic Avatar Generator ===")
    print(f"📸 Processing: {os.path.basename(input_path)}")
    
    # Load image
    if not os.path.exists(input_path):
        raise FileNotFoundError(f"Input image not found: {input_path}")
    
    image = cv2.imread(input_path)
    if image is None:
        raise ValueError(f"Could not load image: {input_path}")
    
    print(f"📏 Original size: {image.shape[1]}x{image.shape[0]}")
    
    # Step 1: Detect face and landmarks
    print("👤 Detecting face and landmarks...")
    face_info = detect_face_landmarks(image)
    
    if face_info:
        print("✅ Face detected successfully!")
        x, y, w, h = face_info['face']
        
        # Add professional padding around face
        padding = int(max(w, h) * 0.5)
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(image.shape[1], x + w + padding)
        y2 = min(image.shape[0], y + h + padding)
        
        # Crop to face region
        face_image = image[y1:y2, x1:x2]
        print(f"📐 Face cropped to: {face_image.shape[1]}x{face_image.shape[0]}")
    else:
        print("⚠️  No face detected, using center crop")
        h, w = image.shape[:2]
        size = min(h, w)
        start_x = (w - size) // 2
        start_y = (h - size) // 2
        face_image = image[start_y:start_y+size, start_x:start_x+size]
    
    # Step 2: Advanced noise reduction
    print("🔧 Applying advanced noise reduction...")
    denoised = reduce_noise_advanced(face_image)
    
    # Step 3: Enhance skin texture and tones
    print("✨ Enhancing skin texture and tones...")
    skin_enhanced = enhance_skin_texture(denoised)
    
    # Step 4: Apply professional lighting
    print("💡 Applying professional lighting...")
    lit_image = apply_professional_lighting(skin_enhanced)
    
    # Step 5: Sharpen details
    print("🔍 Sharpening details...")
    sharpened = sharpen_details(lit_image)
    
    # Step 6: Final color grading
    print("🎨 Applying final color grading...")
    
    # Convert to HSV for final adjustments
    hsv_final = cv2.cvtColor(sharpened, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv_final)
    
    # Final saturation and brightness tweaks
    s = cv2.multiply(s, 1.05)
    v = cv2.multiply(v, 1.02)
    
    s = np.clip(s, 0, 255).astype(np.uint8)
    v = np.clip(v, 0, 255).astype(np.uint8)
    
    final_hsv = cv2.merge([h, s, v])
    final_image = cv2.cvtColor(final_hsv, cv2.COLOR_HSV2BGR)
    
    # Step 7: Resize to standard avatar size
    print("📐 Resizing to standard avatar format...")
    
    # Ensure square aspect ratio
    h, w = final_image.shape[:2]
    size = min(h, w)
    start_x = (w - size) // 2
    start_y = (h - size) // 2
    square_image = final_image[start_y:start_y+size, start_x:start_x+size]
    
    # Resize to 512x512 with high-quality interpolation
    avatar = cv2.resize(square_image, (512, 512), interpolation=cv2.INTER_LANCZOS4)
    
    # Step 8: Final PIL enhancements
    print("🏆 Applying final professional enhancements...")
    
    # Convert to PIL for final touches
    pil_image = Image.fromarray(cv2.cvtColor(avatar, cv2.COLOR_BGR2RGB))
    
    # Final sharpness enhancement
    enhancer = ImageEnhance.Sharpness(pil_image)
    pil_image = enhancer.enhance(1.1)
    
    # Final contrast enhancement
    enhancer = ImageEnhance.Contrast(pil_image)
    pil_image = enhancer.enhance(1.05)
    
    # Very subtle final smoothing
    pil_image = pil_image.filter(ImageFilter.SMOOTH_MORE)
    
    # Step 9: Save with maximum quality
    print("💾 Saving ultra-realistic avatar...")
    pil_image.save(output_path, "JPEG", quality=98, optimize=True)
    
    # Get final file info
    file_size = os.path.getsize(output_path)
    print(f"✅ Ultra-realistic avatar created!")
    print(f"📁 Saved as: {output_path}")
    print(f"📐 Size: 512x512 pixels")
    print(f"💾 File size: {round(file_size/1024, 1)} KB")
    print(f"🏆 Quality: Maximum (98%)")
    
    return output_path

def create_multiple_realistic_styles(input_path):
    """Create multiple ultra-realistic avatar styles."""
    styles = [
        ("professional", "Professional Corporate Style"),
        ("natural", "Natural Enhanced Style"),
        ("portrait", "Portrait Photography Style"),
        ("headshot", "Professional Headshot Style")
    ]
    
    results = []
    
    for style_name, description in styles:
        try:
            print(f"\n--- Creating {description} ---")
            output_name = f"ultra_realistic_{style_name}.jpg"
            
            # Load and process image
            image = cv2.imread(input_path)
            face_info = detect_face_landmarks(image)
            
            if face_info:
                x, y, w, h = face_info['face']
                padding = int(max(w, h) * 0.4)
                x1 = max(0, x - padding)
                y1 = max(0, y - padding)
                x2 = min(image.shape[1], x + w + padding)
                y2 = min(image.shape[0], y + h + padding)
                face_image = image[y1:y2, x1:x2]
            else:
                h, w = image.shape[:2]
                size = min(h, w)
                start_x = (w - size) // 2
                start_y = (h - size) // 2
                face_image = image[start_y:start_y+size, start_x:start_x+size]
            
            # Apply style-specific processing
            if style_name == "professional":
                processed = reduce_noise_advanced(face_image)
                processed = enhance_skin_texture(processed)
                processed = apply_professional_lighting(processed)
                processed = sharpen_details(processed, 1.3)
            elif style_name == "natural":
                processed = reduce_noise_advanced(face_image)
                processed = enhance_skin_texture(processed)
                processed = sharpen_details(processed, 1.1)
            elif style_name == "portrait":
                processed = reduce_noise_advanced(face_image)
                processed = enhance_skin_texture(processed)
                processed = apply_professional_lighting(processed)
                processed = sharpen_details(processed, 1.2)
                # Add slight warmth
                hsv = cv2.cvtColor(processed, cv2.COLOR_BGR2HSV)
                h, s, v = cv2.split(hsv)
                s = cv2.multiply(s, 1.15)
                s = np.clip(s, 0, 255).astype(np.uint8)
                processed = cv2.cvtColor(cv2.merge([h, s, v]), cv2.COLOR_HSV2BGR)
            else:  # headshot
                processed = reduce_noise_advanced(face_image)
                processed = enhance_skin_texture(processed)
                processed = apply_professional_lighting(processed)
                processed = sharpen_details(processed, 1.4)
            
            # Resize and save
            h, w = processed.shape[:2]
            size = min(h, w)
            start_x = (w - size) // 2
            start_y = (h - size) // 2
            square = processed[start_y:start_y+size, start_x:start_x+size]
            final = cv2.resize(square, (512, 512), interpolation=cv2.INTER_LANCZOS4)
            
            # Convert to PIL for final save
            pil_final = Image.fromarray(cv2.cvtColor(final, cv2.COLOR_BGR2RGB))
            pil_final.save(output_name, "JPEG", quality=98, optimize=True)
            
            results.append((style_name, output_name))
            print(f"✅ {description} completed: {output_name}")
            
        except Exception as e:
            print(f"❌ Failed to create {style_name} style: {e}")
    
    return results

def main():
    """Main function to create ultra-realistic avatars."""
    print("🎭 === Ultra-Realistic Human Avatar Generator ===")
    print("Creating the most realistic avatars using advanced computer vision")
    print()
    
    if not os.path.exists(INPUT_PHOTO):
        print(f"❌ Input image not found: {INPUT_PHOTO}")
        return
    
    try:
        # Create main ultra-realistic avatar
        print("🚀 Creating main ultra-realistic avatar...")
        main_avatar = create_ultra_realistic_avatar(INPUT_PHOTO, OUTPUT_PHOTO)
        
        # Create multiple styles
        print("\n🎨 Creating multiple realistic styles...")
        style_results = create_multiple_realistic_styles(INPUT_PHOTO)
        
        print(f"\n🎉 Ultra-realistic avatar generation completed!")
        print(f"📂 Created {len(style_results) + 1} ultra-realistic avatars:")
        print(f"  🏆 Main Avatar: {OUTPUT_PHOTO}")
        for style_name, output_name in style_results:
            print(f"  ✨ {style_name.title()}: {output_name}")
        
        print("\n💡 All avatars feature:")
        print("   ✅ Advanced face detection and cropping")
        print("   ✅ Professional noise reduction")
        print("   ✅ Natural skin texture enhancement")
        print("   ✅ Studio lighting simulation")
        print("   ✅ Professional sharpening")
        print("   ✅ Advanced color grading")
        print("   ✅ Maximum quality output (98%)")
        
    except Exception as e:
        print(f"❌ Error creating avatars: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
